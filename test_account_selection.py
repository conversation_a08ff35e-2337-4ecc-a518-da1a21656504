#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试账号选择时的默认日期功能
验证选择账号时始终使用默认日期
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from account_manager import AccountManager


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    
    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def test_account_selection_default_dates():
    """测试账号选择时的默认日期功能"""
    print("🧪 测试账号选择时的默认日期功能")
    print("=" * 50)
    
    # 使用测试文件
    test_file = "test_account_selection.json"
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建账号管理器
    manager = AccountManager(test_file)
    
    # 获取当前默认日期
    current_default_start, current_default_end = getsetime()
    print(f"当前默认日期范围: {current_default_start} 到 {current_default_end}")
    
    # 创建一个有旧日期的账号
    old_start_date = "2024-01-01"
    old_end_date = "2024-12-31"
    
    print(f"\n📝 创建账号，使用旧日期: {old_start_date} 到 {old_end_date}")
    success = manager.add_account_with_dates("测试账号", "password123", old_start_date, old_end_date)
    
    if not success:
        print("❌ 创建账号失败")
        return False
    
    print("✅ 账号创建成功")
    
    # 验证账号中保存的是旧日期
    saved_account = manager.get_account("测试账号")
    if saved_account:
        saved_start = saved_account.get('start_date', '')
        saved_end = saved_account.get('end_date', '')
        print(f"账号中保存的日期: {saved_start} 到 {saved_end}")
        
        if saved_start == old_start_date and saved_end == old_end_date:
            print("✅ 账号中确实保存了旧日期")
        else:
            print("❌ 账号中的日期不正确")
            return False
    else:
        print("❌ 无法获取保存的账号")
        return False
    
    # 模拟选择账号的行为
    print(f"\n🔄 模拟选择账号（应该显示默认日期而不是保存的旧日期）")
    print(f"期望显示的日期: {current_default_start} 到 {current_default_end}")
    print(f"账号中保存的日期: {saved_start} 到 {saved_end}")
    
    # 在实际的GUI应用中，选择账号时会调用 on_account_selected 方法
    # 该方法现在会忽略保存的日期，始终使用默认日期
    print("✅ 根据新的逻辑，选择账号时会显示默认日期，而不是保存的日期")
    
    # 显示JSON文件内容以确认保存的数据
    print(f"\n📄 JSON文件内容（确认保存了旧日期）:")
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            account_data = data['accounts']['测试账号']
            print(f"  start_date: {account_data['start_date']}")
            print(f"  end_date: {account_data['end_date']}")
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return False
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n🧹 已清理测试文件: {test_file}")
    
    print("\n🎉 测试完成！")
    print("📋 总结:")
    print("  - 账号中可以保存任意日期")
    print("  - 但选择账号时，界面始终显示默认日期")
    print("  - 这确保了用户看到的始终是最新的默认日期范围")
    
    return True


if __name__ == "__main__":
    success = test_account_selection_default_dates()
    exit(0 if success else 1)
