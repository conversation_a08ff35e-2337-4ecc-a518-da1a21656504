#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录窗口界面
PyQt5主界面，包含账号管理和登录功能
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QComboBox,
                             QTextEdit, QMessageBox, QGroupBox, QFormLayout,
                             QSplitter, QFrame, QDateEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon
from account_manager import AccountManager
import traceback
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage
import time
import requests
import json
import urllib.parse
from datetime import datetime, timedelta


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"

    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")

class LoginThread(QThread):
    """登录线程，用于执行DrissionPage登录操作"""

    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号
    login_failed = pyqtSignal(str)   # 登录失败信号
    login_progress = pyqtSignal(str) # 登录进度信号
    data_received = pyqtSignal(str)  # 数据接收信号
    
    def __init__(self, account_info, start_date="", end_date="", account_name=""):
        super().__init__()
        self.account_info = account_info
        self.start_date = start_date
        self.end_date = end_date
        self.account_name = account_name
    
    def run(self):
        """执行登录操作"""
        try:
            self.login_progress.emit("正在初始化浏览器...")
            


            # 获取账号信息
            password = self.account_info.get('password', '')
            account_name = self.account_name

            self.login_progress.emit(f"正在使用账号 {account_name} 登录...")
            self.login_progress.emit(f"查询日期范围: {self.start_date} 到 {self.end_date}")

            # 执行登录逻辑
            page = self.login_to_system(password)

            if page:
                self.login_success.emit(f"✅ 账号 {account_name} 登录成功！已跳转到报关数据查询页面")

                # 获取浏览器cookies并进行数据查询
                self.fetch_data_with_cookies(page, self.start_date, self.end_date)
            else:
                self.login_failed.emit(f"❌ 账号 {account_name} 登录失败")
            
        except Exception as e:
            error_msg = f"❌ 登录过程中发生错误: {str(e)}"
            self.login_failed.emit(error_msg)

    def login_to_system(self, password):
        """
        登录并跳转到报关数据查询
        """
        page = None
        try:
            # 创建ChromiumOptions配置
            self.login_progress.emit("正在配置浏览器选项...")
            co = ChromiumOptions()
            # 可以添加一些浏览器选项
            # co.headless(False)  # 显示浏览器窗口
            # co.set_argument('--disable-blink-features=AutomationControlled')

            # 创建ChromiumPage对象
            self.login_progress.emit("正在创建浏览器页面...")
            page = ChromiumPage(addr_or_opts=co)

            # 访问中国国际贸易单一窗口
            http_path = 'https://app.singlewindow.cn/cas/login?service=https%3A%2F%2Fsz.singlewindow.cn%2Fdyck%2FswProxy%2Fdeskserver%2Fsw%2FdeskIndex%3Fmenu_id%3Ddec001'  # 登录页面地址
            self.login_progress.emit(f"正在访问: {http_path}")
            page.get(http_path)

            self.login_progress.emit(f"页面标题: {page.title}")
            if page.title == "中国国际贸易单一窗口登录管理":
                # 等待并点击卡介质密码登录的按钮
                self.login_progress.emit("正在切换到卡介质密码登录...")
                page.ele("@id=cardTabBtn").wait.has_rect()
                page.ele("@id=cardTabBtn").click()

                # 等待并输入卡介质密码
                self.login_progress.emit("正在输入卡介质密码...")
                page.ele("@id=password").wait.has_rect()
                page.ele("@id=password").input(password)
                time.sleep(1)

                # 等待并点击 Intel 选项框
                self.login_progress.emit("正在选择Intel选项...")
                page.ele("@id=checkboxIntel").wait.has_rect()
                page.ele("@id=checkboxIntel").click()
                time.sleep(1)

                # 等待并点击登录按钮
                self.login_progress.emit("正在点击登录按钮...")
                page.ele("@id=loginbutton").wait.has_rect()
                page.ele("@id=loginbutton").click()

                # 等待页面加载完成
                self.login_progress.emit("等待页面加载完成...")
                time.sleep(10)

            return page

        except Exception as e:
            self.login_progress.emit(f"登录执行失败: {e}")
            if page:
                try:
                    page.quit()
                except:
                    pass
            return None

    def create_custom_query(self, start_date=None, end_date=None, ie_flag="E", etps_category="C"):
        """创建自定义查询参数"""

        # 如果没有指定日期，使用传入的日期
        if not start_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

        # 定义数据字典
        data = {
            "cusCiqNoHidden": "",
            "dclTrnRelFlagHidden": "",
            "transPreNoHidden": "",
            "cusIEFlagHidden": "",
            "cusOrgCode": "",
            "dclTrnRelFlag": "0",
            "cusDecStatus": "",
            "etpsCategory": etps_category,
            "cusIEFlag": ie_flag,
            "entryId": "",
            "cusCiqNo": "",
            "cnsnTradeCode": "",
            "billNo": "",
            "customMaster": "",
            "tableFlag": "1",
            "updateTime": start_date,
            "updateTimeEnd": end_date,
            "operateDate": "1",
            "verifyCode": "",
            "queryPage": "cusBasicQuery",
            "operType": "0"
        }
        # 将数据转换为JSON字符串
        json_str = json.dumps(data)

        # 执行两次encodeURI操作
        encoded_once = urllib.parse.quote(json_str, safe='~()*!.\'')
        encoded_twice = urllib.parse.quote(encoded_once, safe='~()*!.\'')

        return encoded_twice

    def fetch_data_with_cookies(self, page, start_date, end_date):
        """使用浏览器cookies获取数据"""
        try:
            self.login_progress.emit("正在获取浏览器cookies...")

            # 获取浏览器cookies - 使用正确的DrissionPage API
            try:
                # 使用正确的 cookies() 方法
                cookies = page.cookies()
                self.login_progress.emit(f"获取到 {len(cookies)} 个cookies")

                # 转换cookies格式为requests可用的格式
                cookies_dict = {}

                # DrissionPage的CookiesList可以直接迭代
                for cookie in cookies:
                    # 每个cookie应该有name和value属性
                    if hasattr(cookie, 'name') and hasattr(cookie, 'value'):
                        cookies_dict[cookie.name] = cookie.value
                    elif isinstance(cookie, dict):
                        cookies_dict[cookie.get('name', '')] = cookie.get('value', '')

                self.login_progress.emit(f"转换后的cookies数量: {len(cookies_dict)}")

                # 如果没有获取到cookies，直接报错
                if not cookies_dict:
                    raise Exception("未获取到有效的cookies")

            except Exception as cookie_error:
                self.login_progress.emit(f"❌ 获取cookies失败: {str(cookie_error)}")
                return  # 直接返回，不继续执行

            self.login_progress.emit(f"正在查询数据: {start_date} 到 {end_date}")

            # 创建查询参数
            encoded_params = self.create_custom_query(start_date, end_date, "E")

            # 设置请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Pragma': 'no-cache',
                'Referer': 'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/cusQueryZhNew?ngBasePath=https%3A%2F%2Fsz.singlewindow.cn%3A443%2Fdyck%2FswProxy%2Fdecserver%2F',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'content-info': '1il|i1l|l|i1l1il',
                'rdtime': 'jA1WLCP80QUtJkx7',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            # 设置请求参数
            params = {
                'limit': '10',
                'offset': '0',
                'stName': 'updateTime',
                'stOrder': 'desc',
                'decStatusInfo': encoded_params,
                '_': str(int(datetime.now().timestamp() * 1000)),  # 当前时间戳
            }

            # 发送请求
            self.login_progress.emit("正在发送数据请求...")
            response = requests.get(
                'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/merge/cusQueryNew',
                params=params,
                headers=headers,
                cookies=cookies_dict,
                timeout=30
            )

            if response.status_code == 200:
                self.login_progress.emit("✅ 数据获取成功")
                self.data_received.emit(response.text)
            else:
                self.login_progress.emit(f"❌ 数据获取失败，状态码: {response.status_code}")

        except Exception as e:
            self.login_progress.emit(f"❌ 数据获取过程中发生错误: {str(e)}")


class LoginWindow(QMainWindow):
    """登录窗口主类"""
    
    def __init__(self):
        super().__init__()
        self.account_manager = AccountManager()
        self.login_thread = None
        self.init_ui()
        self.load_accounts_to_combo()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("账号管理与登录系统")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：账号管理区域
        left_widget = self.create_account_management_area()
        splitter.addWidget(left_widget)
        
        # 右侧：日志区域
        right_widget = self.create_log_area()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        # 设置样式
        self.setStyleSheet(self.get_stylesheet())
    
    def create_account_management_area(self):
        """创建账号管理区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 账号选择组
        account_group = QGroupBox("账号选择")
        account_layout = QFormLayout(account_group)
        
        self.account_combo = QComboBox()
        self.account_combo.currentTextChanged.connect(self.on_account_selected)
        account_layout.addRow("选择账号:", self.account_combo)
        
        layout.addWidget(account_group)
        
        # 账号信息组
        info_group = QGroupBox("账号信息")
        info_layout = QFormLayout(info_group)

        self.account_name_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        # 日期选择器
        self.start_date_edit = QDateEdit()
        self.end_date_edit = QDateEdit()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.start_date_edit.setCalendarPopup(True)

        self.end_date_edit.setDate(default_end_date)
        self.end_date_edit.setCalendarPopup(True)

        info_layout.addRow("账号名称:", self.account_name_edit)
        info_layout.addRow("密码:", self.password_edit)
        info_layout.addRow("开始日期:", self.start_date_edit)
        info_layout.addRow("结束日期:", self.end_date_edit)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存账号")
        self.save_button.clicked.connect(self.save_account)
        
        self.delete_button = QPushButton("删除账号")
        self.delete_button.clicked.connect(self.delete_account)
        
        self.clear_button = QPushButton("清空表单")
        self.clear_button.clicked.connect(self.clear_form)
        
        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.login_account)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        return widget
    
    def create_log_area(self):
        """创建日志区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 日志组
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 设置最大文档大小来限制内容
        self.log_text.document().setMaximumBlockCount(1000)
        log_layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_button = QPushButton("清空日志")
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)
        
        layout.addWidget(log_group)
        
        return widget
    
    def load_accounts_to_combo(self):
        """加载账号到下拉框"""
        self.account_combo.clear()
        self.account_combo.addItem("-- 选择账号 --")

        account_names = self.account_manager.get_account_names()
        for name in account_names:
            self.account_combo.addItem(name)

        # 默认选择"西格玛"账号
        if "西格玛" in account_names:
            index = self.account_combo.findText("西格玛")
            if index >= 0:
                self.account_combo.setCurrentIndex(index)
                self.add_log(f"已加载 {len(account_names)} 个账号，默认选择：西格玛")
            else:
                self.add_log(f"已加载 {len(account_names)} 个账号")
        else:
            self.add_log(f"已加载 {len(account_names)} 个账号，未找到西格玛账号")
    
    def on_account_selected(self, account_name):
        """账号选择事件"""
        if account_name == "-- 选择账号 --" or not account_name:
            self.clear_form()
            return

        account = self.account_manager.get_account(account_name)
        if account:
            self.account_name_edit.setText(account_name)
            self.password_edit.setText(account.get('password', ''))

            # 始终使用默认日期，不管账号中是否保存了日期
            default_start_str, default_end_str = getsetime()
            default_start_date = QDate.fromString(default_start_str, "yyyy-MM-dd")
            default_end_date = QDate.fromString(default_end_str, "yyyy-MM-dd")

            # 设置为默认日期
            self.start_date_edit.setDate(default_start_date)
            self.end_date_edit.setDate(default_end_date)

            self.add_log(f"已选择账号: {account_name}")
    
    def save_account(self):
        """保存账号"""
        account_name = self.account_name_edit.text().strip()
        password = self.password_edit.text().strip()

        # 获取当前界面上的日期
        current_start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        current_end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # 获取默认日期范围
        default_start_date, default_end_date = getsetime()

        # 如果当前日期是默认日期，或者是新账号，则使用默认日期
        # 这样确保所有账号都有一致的默认日期范围
        start_date = default_start_date
        end_date = default_end_date

        if not account_name:
            QMessageBox.warning(self, "警告", "请输入账号名称！")
            return

        if not password:
            QMessageBox.warning(self, "警告", "请输入密码！")
            return

        # 检查是否是更新现有账号
        if self.account_manager.account_exists(account_name):
            reply = QMessageBox.question(self, "确认", f"账号 '{account_name}' 已存在，是否更新？\n将使用默认日期范围: {start_date} 到 {end_date}",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                success = self.account_manager.update_account_with_dates(
                    account_name, password, start_date, end_date)
                if success:
                    self.add_log(f"✅ 成功更新账号: {account_name} (日期: {start_date} 到 {end_date})")
                    QMessageBox.information(self, "成功", "账号更新成功！")
                else:
                    self.add_log(f"❌ 更新账号失败: {account_name}")
                    QMessageBox.critical(self, "错误", "账号更新失败！")
        else:
            success = self.account_manager.add_account_with_dates(
                account_name, password, start_date, end_date)
            if success:
                self.add_log(f"✅ 成功添加账号: {account_name} (日期: {start_date} 到 {end_date})")
                QMessageBox.information(self, "成功", "账号添加成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                # 选择新添加的账号
                index = self.account_combo.findText(account_name)
                if index >= 0:
                    self.account_combo.setCurrentIndex(index)
            else:
                self.add_log(f"❌ 添加账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号添加失败！")
    
    def delete_account(self):
        """删除账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要删除的账号！")
            return
        
        if not self.account_manager.account_exists(account_name):
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除账号 '{account_name}' 吗？\n此操作不可撤销！",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success = self.account_manager.delete_account(account_name)
            if success:
                self.add_log(f"✅ 成功删除账号: {account_name}")
                QMessageBox.information(self, "成功", "账号删除成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                self.clear_form()  # 清空表单
            else:
                self.add_log(f"❌ 删除账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号删除失败！")
    
    def clear_form(self):
        """清空表单"""
        self.account_name_edit.clear()
        self.password_edit.clear()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.end_date_edit.setDate(default_end_date)
        self.account_combo.setCurrentIndex(0)
    
    def login_account(self):
        """登录账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要登录的账号！")
            return
        
        account = self.account_manager.get_account(account_name)
        if not account:
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 获取日期信息
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # 创建并启动登录线程
        self.login_thread = LoginThread(account, start_date, end_date, account_name)
        self.login_thread.login_success.connect(self.on_login_success)
        self.login_thread.login_failed.connect(self.on_login_failed)
        self.login_thread.login_progress.connect(self.add_log)
        self.login_thread.data_received.connect(self.on_data_received)
        self.login_thread.finished.connect(self.on_login_finished)
        self.login_thread.start()
        
        self.add_log(f"🚀 开始登录账号: {account_name}")
    
    def on_login_success(self, message):
        """登录成功处理"""
        self.add_log(message)
        QMessageBox.information(self, "登录成功", message)
        
        # 更新最后使用时间
        account_name = self.account_name_edit.text().strip()
        self.account_manager.update_last_used(account_name)
    
    def on_login_failed(self, message):
        """登录失败处理"""
        self.add_log(message)
        QMessageBox.critical(self, "登录失败", message)

    def on_data_received(self, data):
        """数据接收处理"""
        try:
            # 解析JSON数据
            json_data = json.loads(data)

            # 格式化显示数据
            formatted_data = json.dumps(json_data, ensure_ascii=False, indent=2)

            # 在日志中显示数据摘要
            if 'data' in json_data and 'rows' in json_data['data']:
                rows_count = len(json_data['data']['rows'])
                total = json_data['data'].get('total', 0)
                self.add_log(f"📊 获取到 {rows_count} 条记录，总计 {total} 条")

                # 显示前几条记录的摘要
                for i, row in enumerate(json_data['data']['rows'][:3]):  # 只显示前3条
                    entry_id = row.get('entryId', 'N/A')
                    bill_no = row.get('billNo', 'N/A')
                    self.add_log(f"  {i+1}. 报关单号: {entry_id}, 提单号: {bill_no}")

                if rows_count > 3:
                    self.add_log(f"  ... 还有 {rows_count - 3} 条记录")
            else:
                self.add_log("📊 数据格式异常或无数据")

            # 可以在这里添加数据保存逻辑
            self.save_data_to_file(formatted_data)

        except json.JSONDecodeError as e:
            self.add_log(f"❌ 数据解析失败: {str(e)}")
            self.add_log(f"原始数据: {data[:200]}...")  # 只显示前200个字符
        except Exception as e:
            self.add_log(f"❌ 数据处理失败: {str(e)}")

    def save_data_to_file(self, data):
        """保存数据到文件"""
        try:
            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"报关数据_{timestamp}.json"

            # 保存到文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(data)

            self.add_log(f"💾 数据已保存到文件: {filename}")

        except Exception as e:
            self.add_log(f"❌ 保存数据失败: {str(e)}")

    def on_login_finished(self):
        """登录完成处理"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")
    
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")
    
    def get_stylesheet(self):
        """获取样式表"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #0084ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0066cc;
        }
        QPushButton:pressed {
            background-color: #004499;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QLineEdit, QComboBox {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: "Consolas", "Monaco", monospace;
        }
        """


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("账号管理与登录系统")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon('icon.png'))
    
    window = LoginWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
