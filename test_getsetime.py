#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 getsetime() 函数
验证日期计算逻辑是否正确
"""

def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    
    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def test_getsetime():
    """测试 getsetime() 函数"""
    from datetime import datetime, timedelta
    
    print("🧪 测试 getsetime() 函数")
    print("=" * 50)
    
    # 获取当前日期信息
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    weekday_names = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
    
    print(f"当前日期: {current_date.strftime('%Y-%m-%d')} ({weekday_names[current_weekday]})")
    
    # 调用函数获取结果
    start_date, end_date = getsetime()
    
    print(f"计算结果:")
    print(f"  开始日期: {start_date}")
    print(f"  结束日期: {end_date}")
    
    # 验证逻辑
    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    
    # 验证结束日期是星期五
    end_weekday = end_datetime.weekday()
    print(f"  结束日期是: {weekday_names[end_weekday]}")
    
    # 验证日期范围是7天
    date_diff = (end_datetime - start_datetime).days
    print(f"  日期范围: {date_diff + 1} 天")
    
    # 验证结束日期是否是前一个星期五
    if end_weekday == 4:  # 星期五
        print("✅ 结束日期正确（星期五）")
    else:
        print("❌ 结束日期错误（不是星期五）")
    
    if date_diff == 6:  # 7天范围
        print("✅ 日期范围正确（7天）")
    else:
        print("❌ 日期范围错误（不是7天）")
    
    # 显示具体的日期范围
    print(f"\n📅 具体日期范围:")
    for i in range(7):
        date = start_datetime + timedelta(days=i)
        weekday = weekday_names[date.weekday()]
        print(f"  {date.strftime('%Y-%m-%d')} ({weekday})")
    
    return True


if __name__ == "__main__":
    test_getsetime()
