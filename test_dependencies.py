#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包测试脚本
验证所有必需的依赖包是否正确安装
"""

import sys


def test_import(package_name, import_name=None, description=""):
    """测试导入包"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} - 导入成功 {description}")
        return True
    except ImportError as e:
        print(f"❌ {package_name} - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {package_name} - 导入异常: {e}")
        return False


def test_specific_functionality():
    """测试特定功能"""
    print("\n🔧 测试特定功能...")
    
    # 测试pandas Excel功能
    try:
        import pandas as pd
        import openpyxl
        
        # 创建测试DataFrame
        test_data = {'列1': [1, 2], '列2': ['A', 'B']}
        df = pd.DataFrame(test_data)
        
        # 测试Excel导出到内存缓冲区
        from io import BytesIO
        excel_buffer = BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        print("✅ pandas + openpyxl Excel导出功能正常")
        return True
        
    except Exception as e:
        print(f"❌ pandas Excel功能测试失败: {e}")
        return False


def test_pyqt5_functionality():
    """测试PyQt5功能"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 创建应用实例（不显示界面）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ PyQt5 GUI功能正常")
        return True
        
    except Exception as e:
        print(f"❌ PyQt5功能测试失败: {e}")
        return False


def test_drissionpage_functionality():
    """测试DrissionPage功能"""
    try:
        from DrissionPage import ChromiumOptions
        
        # 创建配置对象
        co = ChromiumOptions()
        co.headless(True)
        
        print("✅ DrissionPage 浏览器自动化功能正常")
        return True
        
    except Exception as e:
        print(f"❌ DrissionPage功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 报关单下载工具 - 依赖包测试")
    print("=" * 50)
    
    print(f"🐍 Python版本: {sys.version}")
    print()
    
    # 基础包导入测试
    print("📦 基础包导入测试:")
    
    test_results = []
    
    # 测试各个包
    packages_to_test = [
        ("PyQt5", "PyQt5", "(GUI界面框架)"),
        ("DrissionPage", "DrissionPage", "(浏览器自动化)"),
        ("requests", "requests", "(HTTP请求库)"),
        ("pandas", "pandas", "(数据处理库)"),
        ("openpyxl", "openpyxl", "(Excel读写库)"),
        ("json", "json", "(JSON处理 - 内置)"),
        ("datetime", "datetime", "(日期时间 - 内置)"),
        ("enum", "enum", "(枚举类型 - 内置)"),
        ("tempfile", "tempfile", "(临时文件 - 内置)"),
        ("uuid", "uuid", "(UUID生成 - 内置)")
    ]
    
    for package_name, import_name, description in packages_to_test:
        result = test_import(package_name, import_name, description)
        test_results.append((package_name, result))
    
    # 功能测试
    print("\n🔧 功能测试:")
    
    functionality_tests = [
        ("pandas Excel导出", test_specific_functionality),
        ("PyQt5 GUI", test_pyqt5_functionality),
        ("DrissionPage 浏览器", test_drissionpage_functionality)
    ]
    
    for test_name, test_func in functionality_tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
            test_results.append((test_name, False))
    
    # 统计结果
    print("\n📊 测试结果统计:")
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统已准备就绪")
        print("\n🚀 可以运行报关单下载工具:")
        print("   python login_window.py")
        return True
    else:
        print(f"\n⚠️ 有 {total-passed} 个测试失败")
        print("\n🔧 失败的项目:")
        for name, result in test_results:
            if not result:
                print(f"   ❌ {name}")
        
        print("\n💡 解决建议:")
        print("1. 运行依赖安装脚本: python install_dependencies.py")
        print("2. 手动安装缺失的包: pip install -r requirements.txt")
        print("3. 检查Python版本是否为3.7+")
        return False


if __name__ == "__main__":
    try:
        success = main()
        print("\n" + "="*50)
        if success:
            print("✅ 依赖测试完成 - 系统就绪")
        else:
            print("❌ 依赖测试完成 - 需要修复")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n用户取消测试")
    except Exception as e:
        print(f"\n❌ 测试程序出错: {e}")
        input("按回车键退出...")
