#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认选择西格玛账号功能
验证系统启动时是否自动选择西格玛账号
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from account_manager import AccountManager


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    
    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def test_default_account_selection():
    """测试默认选择西格玛账号功能"""
    print("🧪 测试默认选择西格玛账号功能")
    print("=" * 50)
    
    # 使用测试文件
    test_file = "test_default_account.json"
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建账号管理器
    manager = AccountManager(test_file)
    
    # 获取默认日期
    default_start, default_end = getsetime()
    print(f"默认日期范围: {default_start} 到 {default_end}")
    
    # 创建测试账号，包括西格玛账号
    print("\n📝 创建测试账号...")
    accounts_to_create = [
        ("务完物", "password1"),
        ("西格玛", "xgm20244"),  # 西格玛账号
        ("测试账号", "password3")
    ]
    
    for account_name, password in accounts_to_create:
        success = manager.add_account_with_dates(account_name, password, default_start, default_end)
        if success:
            print(f"✅ 成功创建账号: {account_name}")
        else:
            print(f"❌ 创建账号失败: {account_name}")
            return False
    
    # 获取所有账号名称
    account_names = manager.get_account_names()
    print(f"\n📋 所有账号: {account_names}")
    
    # 模拟加载账号到下拉框的逻辑
    print("\n🔄 模拟加载账号到下拉框...")
    
    # 检查西格玛账号是否存在
    if "西格玛" in account_names:
        print("✅ 找到西格玛账号")
        
        # 模拟下拉框选择逻辑
        # 在实际应用中，这会触发 on_account_selected 方法
        selected_account = "西格玛"
        print(f"🎯 默认选择账号: {selected_account}")
        
        # 验证选择的账号信息
        account_info = manager.get_account(selected_account)
        if account_info:
            print(f"📄 账号信息:")
            print(f"  账号名称: {selected_account}")
            print(f"  密码: {account_info.get('password', '')}")
            print(f"  开始日期: {account_info.get('start_date', '')}")
            print(f"  结束日期: {account_info.get('end_date', '')}")
            
            # 验证日期是否为默认日期
            saved_start = account_info.get('start_date', '')
            saved_end = account_info.get('end_date', '')
            
            if saved_start == default_start and saved_end == default_end:
                print("✅ 账号使用了正确的默认日期")
            else:
                print("❌ 账号日期不正确")
                return False
        else:
            print("❌ 无法获取西格玛账号信息")
            return False
    else:
        print("❌ 未找到西格玛账号")
        return False
    
    # 测试没有西格玛账号的情况
    print("\n🧪 测试没有西格玛账号的情况...")
    
    # 删除西格玛账号
    manager.delete_account("西格玛")
    updated_account_names = manager.get_account_names()
    print(f"删除西格玛后的账号: {updated_account_names}")
    
    if "西格玛" not in updated_account_names:
        print("✅ 西格玛账号已删除")
        print("📝 在这种情况下，系统会显示 '-- 选择账号 --'")
    else:
        print("❌ 西格玛账号删除失败")
        return False
    
    # 显示JSON文件内容
    print(f"\n📄 最终JSON文件内容:")
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(json.dumps(data, ensure_ascii=False, indent=2))
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n🧹 已清理测试文件: {test_file}")
    
    print("\n🎉 所有测试通过！")
    print("📋 功能总结:")
    print("  - 系统启动时会自动选择西格玛账号（如果存在）")
    print("  - 如果西格玛账号不存在，显示默认的 '-- 选择账号 --'")
    print("  - 选择西格玛账号时会自动填充账号信息和默认日期")
    
    return True


if __name__ == "__main__":
    success = test_default_account_selection()
    exit(0 if success else 1)
