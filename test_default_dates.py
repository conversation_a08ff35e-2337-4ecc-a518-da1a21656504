#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认日期功能
验证所有账号保存时都使用默认日期范围
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from account_manager import AccountManager


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    
    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def test_default_dates():
    """测试默认日期功能"""
    print("🧪 测试默认日期功能")
    print("=" * 50)
    
    # 使用测试文件
    test_file = "test_default_dates.json"
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建账号管理器
    manager = AccountManager(test_file)
    
    # 获取默认日期
    default_start, default_end = getsetime()
    print(f"默认日期范围: {default_start} 到 {default_end}")
    
    # 测试添加账号（使用默认日期）
    print("\n📝 测试添加账号...")
    success1 = manager.add_account_with_dates("测试账号1", "password1", default_start, default_end)
    success2 = manager.add_account_with_dates("测试账号2", "password2", default_start, default_end)
    success3 = manager.add_account_with_dates("测试账号3", "password3", default_start, default_end)
    
    if success1 and success2 and success3:
        print("✅ 账号添加成功")
    else:
        print("❌ 账号添加失败")
        return False
    
    # 验证保存的日期
    print("\n🔍 验证保存的日期...")
    all_accounts = manager.get_all_accounts()
    
    for account_name, account_info in all_accounts.items():
        saved_start = account_info.get('start_date', '')
        saved_end = account_info.get('end_date', '')
        
        print(f"账号 {account_name}:")
        print(f"  保存的开始日期: {saved_start}")
        print(f"  保存的结束日期: {saved_end}")
        
        if saved_start == default_start and saved_end == default_end:
            print(f"  ✅ 日期正确")
        else:
            print(f"  ❌ 日期不匹配")
            print(f"     期望: {default_start} 到 {default_end}")
            print(f"     实际: {saved_start} 到 {saved_end}")
            return False
    
    # 测试更新账号（也应该使用默认日期）
    print("\n🔄 测试更新账号...")
    success_update = manager.update_account_with_dates("测试账号1", "new_password1", default_start, default_end)
    
    if success_update:
        print("✅ 账号更新成功")
        
        # 验证更新后的日期
        updated_account = manager.get_account("测试账号1")
        if updated_account:
            updated_start = updated_account.get('start_date', '')
            updated_end = updated_account.get('end_date', '')
            
            if updated_start == default_start and updated_end == default_end:
                print("✅ 更新后日期正确")
            else:
                print("❌ 更新后日期不正确")
                return False
    else:
        print("❌ 账号更新失败")
        return False
    
    # 显示JSON文件内容
    print("\n📄 JSON文件内容:")
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(json.dumps(data, ensure_ascii=False, indent=2))
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n🧹 已清理测试文件: {test_file}")
    
    print("\n🎉 所有测试通过！")
    return True


if __name__ == "__main__":
    success = test_default_dates()
    exit(0 if success else 1)
