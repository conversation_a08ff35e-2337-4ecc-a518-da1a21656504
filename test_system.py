#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理系统测试脚本
用于测试AccountManager类的各项功能
"""

import os
import json
from account_manager import AccountManager


def test_account_manager():
    """测试AccountManager类的功能"""
    print("🧪 开始测试AccountManager类...")
    
    # 使用测试文件，避免影响正式数据
    test_file = "test_accounts.json"
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建账号管理器
    manager = AccountManager(test_file)
    print(f"✅ 成功创建AccountManager实例")
    
    # 测试添加账号
    print("\n📝 测试添加账号功能...")
    success1 = manager.add_account("测试账号1", "user1", "pass1", "第一个测试账号", "https://test1.com")
    success2 = manager.add_account("测试账号2", "user2", "pass2", "第二个测试账号", "https://test2.com")
    success3 = manager.add_account("测试账号3", "user3", "pass3", "第三个测试账号", "https://test3.com")
    
    if success1 and success2 and success3:
        print("✅ 账号添加测试通过")
    else:
        print("❌ 账号添加测试失败")
        return False
    
    # 测试获取账号列表
    print("\n📋 测试获取账号列表...")
    account_names = manager.get_account_names()
    print(f"账号列表: {account_names}")
    if len(account_names) == 3:
        print("✅ 账号列表获取测试通过")
    else:
        print("❌ 账号列表获取测试失败")
        return False
    
    # 测试获取单个账号
    print("\n🔍 测试获取单个账号...")
    account1 = manager.get_account("测试账号1")
    if account1 and account1['username'] == 'user1':
        print("✅ 单个账号获取测试通过")
        print(f"账号信息: {account1}")
    else:
        print("❌ 单个账号获取测试失败")
        return False
    
    # 测试更新账号
    print("\n🔄 测试更新账号...")
    success = manager.update_account("测试账号1", username="new_user1", description="更新后的描述")
    if success:
        updated_account = manager.get_account("测试账号1")
        if updated_account['username'] == 'new_user1' and updated_account['description'] == '更新后的描述':
            print("✅ 账号更新测试通过")
        else:
            print("❌ 账号更新测试失败")
            return False
    else:
        print("❌ 账号更新测试失败")
        return False
    
    # 测试账号存在性检查
    print("\n🔎 测试账号存在性检查...")
    exists1 = manager.account_exists("测试账号1")
    exists2 = manager.account_exists("不存在的账号")
    if exists1 and not exists2:
        print("✅ 账号存在性检查测试通过")
    else:
        print("❌ 账号存在性检查测试失败")
        return False
    
    # 测试获取账号总数
    print("\n🔢 测试获取账号总数...")
    count = manager.get_account_count()
    if count == 3:
        print(f"✅ 账号总数测试通过，当前账号数: {count}")
    else:
        print(f"❌ 账号总数测试失败，期望3个，实际{count}个")
        return False
    
    # 测试删除账号
    print("\n🗑️ 测试删除账号...")
    success = manager.delete_account("测试账号2")
    if success:
        remaining_count = manager.get_account_count()
        if remaining_count == 2:
            print("✅ 账号删除测试通过")
        else:
            print(f"❌ 账号删除测试失败，期望2个账号，实际{remaining_count}个")
            return False
    else:
        print("❌ 账号删除测试失败")
        return False
    
    # 测试JSON文件格式
    print("\n📄 测试JSON文件格式...")
    if os.path.exists(test_file):
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            required_keys = ['accounts', 'last_updated', 'version']
            if all(key in data for key in required_keys):
                print("✅ JSON文件格式测试通过")
                print(f"文件内容预览: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
            else:
                print("❌ JSON文件格式测试失败，缺少必要字段")
                return False
        except Exception as e:
            print(f"❌ JSON文件格式测试失败: {e}")
            return False
    else:
        print("❌ JSON文件不存在")
        return False
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print("🧹 测试文件已清理")
    
    print("\n🎉 所有测试通过！AccountManager类功能正常")
    return True


def test_json_file_operations():
    """测试JSON文件操作"""
    print("\n📁 测试JSON文件操作...")
    
    test_file = "test_json_ops.json"
    
    # 测试文件不存在时的处理
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = AccountManager(test_file)
    
    # 添加一些测试数据
    manager.add_account("JSON测试1", "json_user1", "json_pass1", "JSON测试账号1")
    manager.add_account("JSON测试2", "json_user2", "json_pass2", "JSON测试账号2")
    
    # 重新加载，测试持久化
    manager2 = AccountManager(test_file)
    
    if manager2.get_account_count() == 2:
        print("✅ JSON文件持久化测试通过")
    else:
        print("❌ JSON文件持久化测试失败")
        return False
    
    # 清理
    if os.path.exists(test_file):
        os.remove(test_file)
    
    return True


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    test_file = "test_edge_cases.json"
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = AccountManager(test_file)
    
    # 测试空字符串输入
    success = manager.add_account("", "user", "pass")
    if not success:
        print("✅ 空账号名称拒绝测试通过")
    else:
        print("❌ 空账号名称拒绝测试失败")
        return False
    
    # 测试空用户名
    success = manager.add_account("test", "", "pass")
    if not success:
        print("✅ 空用户名拒绝测试通过")
    else:
        print("❌ 空用户名拒绝测试失败")
        return False
    
    # 测试空密码
    success = manager.add_account("test", "user", "")
    if not success:
        print("✅ 空密码拒绝测试通过")
    else:
        print("❌ 空密码拒绝测试失败")
        return False
    
    # 测试获取不存在的账号
    account = manager.get_account("不存在的账号")
    if account is None:
        print("✅ 获取不存在账号测试通过")
    else:
        print("❌ 获取不存在账号测试失败")
        return False
    
    # 测试删除不存在的账号
    success = manager.delete_account("不存在的账号")
    if not success:
        print("✅ 删除不存在账号测试通过")
    else:
        print("❌ 删除不存在账号测试失败")
        return False
    
    # 清理
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print("✅ 边界情况测试全部通过")
    return True


def main():
    """主测试函数"""
    print("🚀 开始账号管理系统测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test1_passed = test_account_manager()
        test2_passed = test_json_file_operations()
        test3_passed = test_edge_cases()
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        print(f"AccountManager功能测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
        print(f"JSON文件操作测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
        print(f"边界情况测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
        
        if all([test1_passed, test2_passed, test3_passed]):
            print("\n🎉 所有测试通过！系统功能正常")
            return True
        else:
            print("\n❌ 部分测试失败，请检查代码")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
