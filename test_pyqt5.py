#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyQt5是否正常工作
"""

import sys
import os

def test_pyqt5_import():
    """测试PyQt5导入"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        print("✅ PyQt5导入成功")
        return True
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False

def test_simple_window():
    """测试简单窗口"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt, QTimer
        
        # 设置环境变量
        os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = ''
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("PyQt5测试窗口")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("PyQt5测试成功！\n如果您看到这个窗口，说明PyQt5工作正常。")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # 显示窗口
        window.show()
        
        # 设置定时器自动关闭窗口
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3秒后关闭
        
        print("✅ 测试窗口创建成功，将在3秒后自动关闭")
        
        # 运行应用
        app.exec_()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试窗口失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 开始测试PyQt5...")
    
    # 测试导入
    if not test_pyqt5_import():
        return False
    
    # 测试简单窗口
    if not test_simple_window():
        return False
    
    print("🎉 PyQt5测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
