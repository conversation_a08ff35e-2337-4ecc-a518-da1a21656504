#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试枚举优化功能
验证 table_flag 等参数的枚举实现
"""

import sys
import os
import json

# 添加当前目录到路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_enum_definitions():
    """测试枚举定义"""
    print("🧪 测试枚举定义")
    print("=" * 50)
    
    try:
        # 导入枚举类
        from login_window import TableFlag, IEFlag, EtpsCategory
        
        print("✅ 枚举类导入成功")
        
        # 测试 TableFlag 枚举
        print("\n📋 TableFlag 枚举测试:")
        print(f"  NO (否): {TableFlag.NO.value}")
        print(f"  YES (是): {TableFlag.YES.value}")
        print(f"  描述 - NO: {TableFlag.get_description(TableFlag.NO.value)}")
        print(f"  描述 - YES: {TableFlag.get_description(TableFlag.YES.value)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 枚举类导入失败: {e}")
        return False
    except AssertionError as e:
        print(f"❌ 枚举值验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False




if __name__ == "__main__":
    print("📊 枚举优化功能测试")
    print("=" * 60)
    
    success1 = test_enum_definitions()